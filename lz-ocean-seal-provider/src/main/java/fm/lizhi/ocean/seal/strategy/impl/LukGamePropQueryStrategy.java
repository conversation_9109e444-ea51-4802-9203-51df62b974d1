package fm.lizhi.ocean.seal.strategy.impl;

import com.alibaba.fastjson.JSONObject;
import com.google.inject.Inject;
import com.netflix.governator.annotations.AutoBindSingleton;
import fm.lizhi.ocean.seal.constant.GameChannel;
import fm.lizhi.ocean.seal.constant.LukControlEventType;
import fm.lizhi.ocean.seal.dao.bean.GameInfoBean;
import fm.lizhi.ocean.seal.manager.BizGameManager;
import fm.lizhi.ocean.seal.manager.GameChannelManager;
import fm.lizhi.ocean.seal.manager.GameInfoManager;
import fm.lizhi.ocean.seal.manager.LukManger;
import fm.lizhi.ocean.seal.pojo.UserPropStatusResult;
import fm.lizhi.ocean.seal.pojo.PropGrantStatusResult;
import fm.lizhi.ocean.seal.protocol.GamePropServiceProto.GetUserPropStatusParam;
import fm.lizhi.ocean.seal.protocol.GamePropServiceProto.QueryPropGrantStatusParam;
import fm.lizhi.ocean.seal.strategy.GamePropQueryStrategy;
import io.github.cfgametech.Response;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * LUK 渠道道具查询策略实现
 * <AUTHOR>
 */
@Slf4j
@AutoBindSingleton
public class LukGamePropQueryStrategy implements GamePropQueryStrategy {

    @Inject
    private LukManger lukManager;

    @Inject
    private GameInfoManager gameInfoManager;

    @Override
    public boolean supports(String channel) {
        return GameChannel.LUK.equals(channel);
    }

    @Override
    public UserPropStatusResult getUserPropStatus(GetUserPropStatusParam param) {
        log.info("LUK get user prop status, gameId: {}, userId: {}", param.getChannelGameId(), param.getUserId());

        GameInfoBean gameInfoBean = gameInfoManager.getGameInfoPO(param.getChannel(), Long.parseLong(param.getChannelGameId()));
        if (gameInfoBean == null) {
            throw new IllegalArgumentException("gameInfo not exist. appId=" + param.getAppId() + ", gameId=" + param.getChannelGameId());
        }

        // 构建获取背包状态的数据
        Map<String, Object> eventData = new HashMap<>();
        eventData.put("user_id", param.getUserId());

        // 调用 LUK SDK
        Response<?> sdkResult = lukManager.publishGlobalEvent(
                param.getAppId(),
                String.valueOf(gameInfoBean.getChannelGameId()),
                LukControlEventType.GET_USER_PROPS,
                eventData
        );

        if (sdkResult.suc()) {
            log.info("LUK get user props success, data: {}", sdkResult.getData());
        } else {
            log.warn("LUK get user props failed, code: {}, message: {}", sdkResult.getCode(), sdkResult.getMessage());
        }
        // 转换结果
        return convertLukUserPropsResult(sdkResult, param);

    }

    @Override
    public PropGrantStatusResult queryPropGrantStatus(QueryPropGrantStatusParam param) {

        GameInfoBean gameInfoBean = gameInfoManager.getGameInfoPO(param.getChannel(), Long.parseLong(param.getChannelGameId()));
        if (gameInfoBean == null) {
            throw new IllegalArgumentException("gameInfo not exist. appId=" + param.getAppId() + ", channelGameId=" + param.getChannelGameId());
        }

        // 构建查询道具发放状态的数据
        Map<String, Object> eventData = new HashMap<>();
        eventData.put("unique_id", param.getUniqueId());

        // 调用 LUK SDK
        Response<?> sdkResult = lukManager.publishGlobalEvent(
                param.getAppId(),
                String.valueOf(gameInfoBean.getChannelGameId()),
                LukControlEventType.QUERY_PROP_STATUS,
                eventData
        );
        if (sdkResult.suc()) {
            log.info("LUK query prop status success, data: {}", sdkResult.getData());
        } else {
            log.warn("LUK query prop status failed, code: {}, message: {}", sdkResult.getCode(), sdkResult.getMessage());
        }

        // 转换结果
        return convertLukPropStatusResult(sdkResult, param);

    }

    /**
     * 转换 LUK 用户背包状态结果
     */
    private UserPropStatusResult convertLukUserPropsResult(Response<?> sdkResult, GetUserPropStatusParam param) {
        if (sdkResult == null) {
            throw new RuntimeException("LUK SDK 调用失败");
        }

        if (sdkResult.getCode() != 0) {
            log.warn("LUK get user props failed, code: {}, message: {}", sdkResult.getCode(), sdkResult.getMessage());
            throw new RuntimeException("LUK 查询失败: " + sdkResult.getMessage());
        }

        // 解析 LUK 返回的数据
        Object data = sdkResult.getData();
        if (data == null) {
            return UserPropStatusResult.success(new ArrayList<>());
        }

        JSONObject dataJson = (JSONObject) JSONObject.toJSON(data);
        List<Object> propsArray = dataJson.getJSONArray("props");

        List<UserPropStatusResult.UserPropStatusInfo> userProps = new ArrayList<>();
        if (propsArray != null) {
            for (Object propObj : propsArray) {
                JSONObject prop = (JSONObject) JSONObject.toJSON(propObj);
                UserPropStatusResult.UserPropStatusInfo userProp = new UserPropStatusResult.UserPropStatusInfo()
                        .setPropId(prop.getString("prop_id"))
                        .setType(prop.getInteger("type"))
                        .setNum(prop.getInteger("num"))
                        .setExpireTime(prop.getLong("expire_time"));
                userProps.add(userProp);
            }
        }

        return UserPropStatusResult.success(userProps);
    }

    /**
     * 转换 LUK 道具发放状态结果
     */
    private PropGrantStatusResult convertLukPropStatusResult(Response<?> sdkResult, QueryPropGrantStatusParam param) {
        if (sdkResult == null) {
            throw new RuntimeException("LUK SDK 调用失败");
        }

        if (sdkResult.getCode() != 0) {
            log.warn("LUK query prop status failed, code: {}, message: {}", sdkResult.getCode(), sdkResult.getMessage());
            throw new RuntimeException("LUK 查询失败: " + sdkResult.getMessage());
        }

        // 解析 LUK 返回的数据
        Object data = sdkResult.getData();
        if (data == null) {
            return PropGrantStatusResult.success(null);
        }

        JSONObject dataJson = (JSONObject) JSONObject.toJSON(data);

        // 解析道具详情
        List<PropGrantStatusResult.PropGrantDetailInfo> details = new ArrayList<>();
        List<Object> propsArray = dataJson.getJSONArray("props");
        if (propsArray != null) {
            for (Object propObj : propsArray) {
                JSONObject prop = (JSONObject) JSONObject.toJSON(propObj);
                PropGrantStatusResult.PropGrantDetailInfo detail = new PropGrantStatusResult.PropGrantDetailInfo()
                        .setPropId(prop.getString("prop_id"))
                        .setNum(prop.getInteger("num"))
                        .setDuration(prop.getInteger("duration"))
                        .setDurationReset(prop.getBoolean("duration_reset"));
                details.add(detail);
            }
        }

        // 构建发放状态信息
        PropGrantStatusResult.PropGrantStatusInfo statusInfo = new PropGrantStatusResult.PropGrantStatusInfo()
                .setAppId(param.getAppId())
                .setGameId(param.getChannelGameId())
                .setUniqueId(dataJson.getString("unique_id"))
                .setUserId(dataJson.getString("user_id"))
                .setDetails(details)
                .setExtra(dataJson.getString("extra"))
                .setStatus(dataJson.getInteger("status"))
                .setCreatedTime(dataJson.getLong("created_time"));

        return PropGrantStatusResult.success(statusInfo);

    }
}
