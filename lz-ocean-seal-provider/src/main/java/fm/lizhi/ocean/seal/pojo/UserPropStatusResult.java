package fm.lizhi.ocean.seal.pojo;

import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * 用户道具状态查询结果
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class UserPropStatusResult {

    /**
     * 用户道具状态列表
     */
    private List<UserPropStatusInfo> userProps;

    /**
     * 用户道具状态信息
     */
    @Data
    @Accessors(chain = true)
    public static class UserPropStatusInfo {
        /**
         * 道具ID
         */
        private String propId;

        /**
         * 道具类型（1：时效性道具 2：非时效性道具）
         */
        private Integer type;

        /**
         * 道具数量
         */
        private Integer num;

        /**
         * 过期时间戳（秒），小于0为永久
         */
        private Long expireTime;
    }

    /**
     * 创建成功结果
     */
    public static UserPropStatusResult success(List<UserPropStatusInfo> userProps) {
        return new UserPropStatusResult().setUserProps(userProps);
    }
}
