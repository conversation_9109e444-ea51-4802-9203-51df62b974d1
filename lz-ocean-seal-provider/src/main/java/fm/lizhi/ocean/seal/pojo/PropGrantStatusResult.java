package fm.lizhi.ocean.seal.pojo;

import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * 道具发放状态查询结果
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class PropGrantStatusResult {

    /**
     * 道具发放状态信息
     */
    private PropGrantStatusInfo propGrantStatus;

    /**
     * 道具发放状态信息
     */
    @Data
    @Accessors(chain = true)
    public static class PropGrantStatusInfo {
        /**
         * APP ID
         */
        private String appId;

        /**
         * 游戏ID
         */
        private String gameId;

        /**
         * 唯一ID
         */
        private String uniqueId;

        /**
         * 用户ID
         */
        private String userId;

        /**
         * 道具详情
         */
        private List<PropGrantDetailInfo> details;

        /**
         * 附加信息
         */
        private String extra;

        /**
         * 发放状态（1：成功 0：失败）
         */
        private Integer status;

        /**
         * 创建时间戳
         */
        private Long createdTime;
    }

    /**
     * 道具发放详情
     */
    @Data
    @Accessors(chain = true)
    public static class PropGrantDetailInfo {
        /**
         * 道具ID
         */
        private String propId;

        /**
         * 发放数量
         */
        private Integer num;

        /**
         * 有效时长（秒）
         */
        private Integer duration;

        /**
         * 是否重置时间
         */
        private Boolean durationReset;
    }

    /**
     * 创建成功结果
     */
    public static PropGrantStatusResult success(PropGrantStatusInfo propGrantStatus) {
        return new PropGrantStatusResult().setPropGrantStatus(propGrantStatus);
    }
}
