package fm.lizhi.ocean.seal.strategy;

import fm.lizhi.ocean.seal.pojo.UserPropStatusResult;
import fm.lizhi.ocean.seal.pojo.PropGrantStatusResult;
import fm.lizhi.ocean.seal.protocol.GamePropServiceProto.GetUserPropStatusParam;
import fm.lizhi.ocean.seal.protocol.GamePropServiceProto.QueryPropGrantStatusParam;

/**
 * 游戏道具查询策略接口
 * <AUTHOR>
 */
public interface GamePropQueryStrategy {

    /**
     * 获取用户背包状态
     * @param param 查询参数
     * @return 查询结果
     */
    UserPropStatusResult getUserPropStatus(GetUserPropStatusParam param);

    /**
     * 查询道具发放状态
     * @param param 查询参数
     * @return 查询结果
     */
    PropGrantStatusResult queryPropGrantStatus(QueryPropGrantStatusParam param);

    /**
     * 是否支持该渠道
     * @param channel 渠道名称
     * @return 是否支持
     */
    boolean supports(String channel);
}
